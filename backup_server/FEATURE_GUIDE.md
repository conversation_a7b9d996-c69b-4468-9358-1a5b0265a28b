# 视频备份系统功能使用指南

## 新增功能概览

本次更新为视频备份系统添加了三个重要功能：

1. **本地云端数据同步机制** - 智能同步管理，避免重复下载
2. **增强的前端筛选功能** - 只显示需要下载的视频
3. **本地视频分类管理** - 按设备和猫分类查看视频

## 功能详细说明

### 1. 数据同步机制

#### 同步数据库
系统会自动维护一个 `sync_database.json` 文件，记录所有下载的视频信息：

```json
{
  "version": "1.0",
  "last_sync": "2025-01-24T10:30:15Z",
  "records": {
    "video_123": {
      "video_id": "video_123",
      "device_id": "device202502270220f7cbb4421000",
      "animal_id": "cat_456",
      "start_time": 1737712707,
      "downloaded_at": "2025-01-24T10:30:15Z",
      "local_path": "./data/by_device/device202502270220f7cbb4421000/2025-01-23_20-46-05_hls",
      "file_size": 52428800,
      "file_count": 15,
      "status": "downloaded",
      "checksum": "abc123def456"
    }
  },
  "deleted_ids": [],
  "total_count": 1,
  "total_size": 52428800
}
```

#### 同步状态类型
- **synced**: 本地和云端都存在，已同步
- **remote_only**: 仅云端存在，可下载
- **local_only**: 仅本地存在，云端已删除
- **conflict**: 存在冲突，需要手动处理

#### 自动同步功能
- 下载完成后自动添加同步记录
- 删除本地视频时自动更新同步状态
- 云端视频列表自动过滤已下载的视频

### 2. 智能筛选功能

#### 云端视频页面
- **自动过滤**: 已下载的视频不会在云端视频列表中显示
- **智能提示**: 页面顶部显示筛选状态说明
- **避免重复**: 确保不会重复下载相同视频

#### 同步管理页面
提供完整的同步状态管理：

1. **同步摘要卡片**
   - 已同步视频数量
   - 仅云端存在数量
   - 仅本地存在数量
   - 冲突数量
   - 云端和本地总数

2. **筛选选项**
   - 全部视频
   - 已同步
   - 仅云端存在（可下载）
   - 仅本地存在
   - 冲突

3. **操作功能**
   - 刷新同步状态
   - 刷新同步数据库

### 3. 分类管理功能

#### 按设备分类
- 显示所有设备列表
- 每个设备显示视频数量和总大小
- 点击设备查看该设备的所有视频
- 支持分页浏览

#### 按猫分类
- 显示所有猫咪列表
- 每只猫显示视频数量和总大小
- 点击猫咪查看该猫的所有视频
- 支持分页浏览

## 使用流程

### 首次使用

1. **启动系统**
   ```bash
   cd backup_server
   ./start.sh
   ```

2. **访问Web界面**
   打开浏览器访问 `http://localhost:8080`

3. **查看云端视频**
   - 切换到"云端视频"标签页
   - 系统会自动显示未下载的视频
   - 选择需要的视频进行下载

### 日常使用

#### 下载新视频
1. 在"云端视频"页面查看可下载的视频
2. 选择需要的视频（支持多选）
3. 点击"下载选中"开始下载
4. 在"下载任务"页面监控进度

#### 查看同步状态
1. 切换到"同步管理"标签页
2. 查看同步摘要了解整体状态
3. 使用筛选功能查看特定状态的视频
4. 如有需要，点击"刷新同步数据库"

#### 分类管理视频
1. 在"本地视频"页面点击"分类查看"
2. 选择"按设备查看"或"按猫查看"
3. 点击具体的设备或猫查看详细视频列表
4. 可以删除不需要的视频

### 高级功能

#### 同步数据库维护
- **自动维护**: 系统会自动维护同步数据库
- **手动刷新**: 如果数据不一致，可以手动刷新
- **数据恢复**: 删除 `sync_database.json` 文件，系统会重新扫描本地文件

#### 冲突处理
当出现冲突时（本地有但云端没有，或数据不一致）：
1. 在同步管理页面筛选"冲突"状态
2. 查看具体的冲突视频
3. 根据需要决定保留或删除本地文件

## API接口

### 同步管理API

```bash
# 获取同步摘要
GET /api/sync/summary

# 获取筛选后的视频列表
GET /api/sync/videos?filter=remote_only&page=1&page_size=20

# 刷新同步数据库
POST /api/sync/refresh
```

### 分类管理API

```bash
# 获取设备列表
GET /api/categories/devices

# 获取猫列表
GET /api/categories/cats

# 按设备获取视频
GET /api/categories/devices/{device_id}/videos?page=1&page_size=20

# 按猫获取视频
GET /api/categories/cats/{cat_id}/videos?page=1&page_size=20

# 获取设备统计
GET /api/categories/devices/stats

# 获取猫统计
GET /api/categories/cats/stats
```

## 数据文件说明

### 同步数据库文件
- **位置**: `data/sync_database.json`
- **作用**: 记录所有下载的视频信息和同步状态
- **备份**: 建议定期备份此文件

### 视频元数据文件
- **位置**: `data/by_device/{device_id}/{video_folder}/metadata.json`
- **作用**: 记录单个视频的详细信息
- **格式**: JSON格式，包含视频ID、设备ID、猫ID等信息

### 符号链接结构
- **设备分类**: `data/by_device/{device_id}/`
- **猫分类**: `data/by_cat/{cat_id}/` (符号链接)
- **优势**: 避免重复存储，节省磁盘空间

## 故障排除

### 同步数据不一致
1. 点击"刷新同步数据库"按钮
2. 如果问题持续，删除 `sync_database.json` 文件重新启动

### 分类视图显示异常
1. 检查符号链接是否正确创建
2. 运行存储清理功能
3. 重新下载有问题的视频

### 筛选功能不工作
1. 确保同步数据库正常
2. 检查网络连接到backend_server
3. 查看浏览器控制台错误信息

## 性能优化建议

1. **定期清理**: 删除不需要的视频文件
2. **监控磁盘**: 确保有足够的存储空间
3. **网络优化**: 根据网络状况调整并发下载数
4. **数据备份**: 定期备份同步数据库和重要视频

## 更新日志

### v2.0 (当前版本)
- ✅ 添加本地云端数据同步机制
- ✅ 实现智能视频筛选功能
- ✅ 支持按设备和猫分类管理
- ✅ 新增同步管理界面
- ✅ 优化用户体验和界面设计

### v1.0 (初始版本)
- ✅ 基础视频下载功能
- ✅ 任务队列管理
- ✅ Web界面
- ✅ 存储管理功能
