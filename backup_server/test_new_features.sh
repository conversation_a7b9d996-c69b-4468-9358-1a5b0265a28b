#!/bin/bash

# 新功能测试脚本

set -e

echo "=== 视频备份系统新功能测试 ==="

# 配置
BACKUP_SERVER_URL="http://localhost:8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local url=$1
    local method=${2:-GET}
    local data=${3:-}
    local expected_status=${4:-200}
    
    echo -n "测试 $method $url ... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$url")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗ (状态码: $status_code)${NC}"
        echo "响应: $body"
        return 1
    fi
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "           新功能测试 v2.0"
    echo "=================================================="
    echo -e "${NC}"
}

# 测试同步功能
test_sync_features() {
    echo -e "${YELLOW}1. 测试同步管理功能...${NC}"
    
    echo "1.1 测试获取同步摘要"
    test_api "$BACKUP_SERVER_URL/api/sync/summary"
    
    echo "1.2 测试获取筛选视频列表"
    test_api "$BACKUP_SERVER_URL/api/sync/videos?filter=all&page=1&page_size=5"
    
    echo "1.3 测试筛选仅云端存在的视频"
    test_api "$BACKUP_SERVER_URL/api/sync/videos?filter=remote_only&page=1&page_size=5"
    
    echo "1.4 测试刷新同步数据库"
    test_api "$BACKUP_SERVER_URL/api/sync/refresh" "POST"
    
    echo -e "${GREEN}✓ 同步管理功能测试完成${NC}"
    echo ""
}

# 测试分类管理功能
test_category_features() {
    echo -e "${YELLOW}2. 测试分类管理功能...${NC}"
    
    echo "2.1 测试获取设备列表"
    test_api "$BACKUP_SERVER_URL/api/categories/devices"
    
    echo "2.2 测试获取猫列表"
    test_api "$BACKUP_SERVER_URL/api/categories/cats"
    
    echo "2.3 测试获取设备统计"
    test_api "$BACKUP_SERVER_URL/api/categories/devices/stats"
    
    echo "2.4 测试获取猫统计"
    test_api "$BACKUP_SERVER_URL/api/categories/cats/stats"
    
    # 获取设备列表进行进一步测试
    echo "2.5 测试按设备获取视频"
    devices_response=$(curl -s "$BACKUP_SERVER_URL/api/categories/devices")
    if echo "$devices_response" | grep -q '"devices":\['; then
        # 提取第一个设备ID（简单的JSON解析）
        device_id=$(echo "$devices_response" | grep -o '"[^"]*"' | head -1 | tr -d '"')
        if [ -n "$device_id" ] && [ "$device_id" != "devices" ]; then
            test_api "$BACKUP_SERVER_URL/api/categories/devices/$device_id/videos?page=1&page_size=5"
        else
            echo -e "${YELLOW}⚠ 没有设备数据，跳过设备视频测试${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 无法获取设备列表，跳过设备视频测试${NC}"
    fi
    
    echo "2.6 测试按猫获取视频"
    cats_response=$(curl -s "$BACKUP_SERVER_URL/api/categories/cats")
    if echo "$cats_response" | grep -q '"cats":\['; then
        # 提取第一个猫ID
        cat_id=$(echo "$cats_response" | grep -o '"[^"]*"' | head -1 | tr -d '"')
        if [ -n "$cat_id" ] && [ "$cat_id" != "cats" ]; then
            test_api "$BACKUP_SERVER_URL/api/categories/cats/$cat_id/videos?page=1&page_size=5"
        else
            echo -e "${YELLOW}⚠ 没有猫数据，跳过猫视频测试${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 无法获取猫列表，跳过猫视频测试${NC}"
    fi
    
    echo -e "${GREEN}✓ 分类管理功能测试完成${NC}"
    echo ""
}

# 测试智能筛选功能
test_filtering_features() {
    echo -e "${YELLOW}3. 测试智能筛选功能...${NC}"
    
    echo "3.1 测试云端视频列表（应该已过滤）"
    test_api "$BACKUP_SERVER_URL/api/videos/remote?page=1&page_size=5"
    
    echo "3.2 测试本地视频列表"
    test_api "$BACKUP_SERVER_URL/api/videos/local?page=1&page_size=5"
    
    echo -e "${GREEN}✓ 智能筛选功能测试完成${NC}"
    echo ""
}

# 测试Web界面
test_web_interface() {
    echo -e "${YELLOW}4. 测试Web界面...${NC}"
    
    echo "4.1 测试主页面"
    test_api "$BACKUP_SERVER_URL/"
    
    echo "4.2 检查页面是否包含新功能"
    page_content=$(curl -s "$BACKUP_SERVER_URL/")
    
    if echo "$page_content" | grep -q "同步管理"; then
        echo -e "${GREEN}✓ 同步管理标签页存在${NC}"
    else
        echo -e "${RED}✗ 同步管理标签页缺失${NC}"
    fi
    
    if echo "$page_content" | grep -q "分类查看"; then
        echo -e "${GREEN}✓ 分类查看功能存在${NC}"
    else
        echo -e "${RED}✗ 分类查看功能缺失${NC}"
    fi
    
    if echo "$page_content" | grep -q "智能筛选"; then
        echo -e "${GREEN}✓ 智能筛选提示存在${NC}"
    else
        echo -e "${RED}✗ 智能筛选提示缺失${NC}"
    fi
    
    echo -e "${GREEN}✓ Web界面测试完成${NC}"
    echo ""
}

# 检查数据文件
check_data_files() {
    echo -e "${YELLOW}5. 检查数据文件...${NC}"
    
    if [ -f "data/sync_database.json" ]; then
        echo -e "${GREEN}✓ 同步数据库文件存在${NC}"
        
        # 检查文件格式
        if jq empty data/sync_database.json 2>/dev/null; then
            echo -e "${GREEN}✓ 同步数据库格式正确${NC}"
        else
            echo -e "${YELLOW}⚠ 同步数据库格式可能有问题${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 同步数据库文件不存在（首次运行正常）${NC}"
    fi
    
    if [ -d "data/by_device" ]; then
        echo -e "${GREEN}✓ 设备分类目录存在${NC}"
    else
        echo -e "${YELLOW}⚠ 设备分类目录不存在${NC}"
    fi
    
    if [ -d "data/by_cat" ]; then
        echo -e "${GREEN}✓ 猫分类目录存在${NC}"
    else
        echo -e "${YELLOW}⚠ 猫分类目录不存在${NC}"
    fi
    
    echo -e "${GREEN}✓ 数据文件检查完成${NC}"
    echo ""
}

# 生成测试报告
generate_report() {
    echo ""
    echo -e "${BLUE}=== 新功能测试报告 ===${NC}"
    echo "测试时间: $(date)"
    echo "backup_server URL: $BACKUP_SERVER_URL"
    echo ""
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 所有新功能测试通过！${NC}"
        echo ""
        echo "新功能已成功集成："
        echo "✅ 本地云端数据同步机制"
        echo "✅ 智能视频筛选功能"
        echo "✅ 按设备和猫分类管理"
        echo "✅ 同步管理界面"
        echo "✅ 增强的用户体验"
        echo ""
        echo "可以开始使用新功能了！"
        echo "详细使用说明请查看: FEATURE_GUIDE.md"
    else
        echo -e "${RED}❌ 部分测试失败 ($((total_tests - failed_tests))/$total_tests 通过)${NC}"
        echo ""
        echo "请检查以下问题："
        echo "1. 确认服务正常运行"
        echo "2. 检查网络连接"
        echo "3. 查看服务日志"
        echo "4. 确认配置文件正确"
    fi
}

# 主测试流程
main() {
    show_banner
    
    total_tests=0
    failed_tests=0
    
    # 检查服务是否运行
    echo "检查服务状态..."
    if ! curl -s "$BACKUP_SERVER_URL/api/health" > /dev/null; then
        echo -e "${RED}错误: backup_server服务未运行或无法访问${NC}"
        echo "请先启动服务: ./start.sh"
        exit 1
    fi
    echo -e "${GREEN}✓ 服务运行正常${NC}"
    echo ""
    
    # 运行测试
    tests=(
        "test_sync_features"
        "test_category_features"
        "test_filtering_features"
        "test_web_interface"
        "check_data_files"
    )
    
    for test in "${tests[@]}"; do
        if ! $test; then
            failed_tests=$((failed_tests + 1))
        fi
        total_tests=$((total_tests + 1))
    done
    
    generate_report
    
    # 返回适当的退出码
    if [ $failed_tests -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "视频备份系统新功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --url URL  指定backup_server URL (默认: $BACKUP_SERVER_URL)"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认配置运行测试"
    echo "  $0 -u http://localhost:9090  # 指定自定义URL"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BACKUP_SERVER_URL="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主程序
main
