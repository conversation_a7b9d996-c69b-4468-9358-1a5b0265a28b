package handlers

import (
	"backup_server/pkg/models"
	"backup_server/pkg/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	backendClient   *services.BackendClient
	downloadService *services.DownloadService
	storageService  *services.StorageService
	syncService     *services.SyncService
}

// NewHandler 创建处理器
func NewHandler(backendClient *services.BackendClient, downloadService *services.DownloadService, storageService *services.StorageService, syncService *services.SyncService) *Handler {
	return &Handler{
		backendClient:   backendClient,
		downloadService: downloadService,
		storageService:  storageService,
		syncService:     syncService,
	}
}

// GetVideoList 获取云端视频列表
func (h *Handler) GetVideoList(c *gin.Context) {
	var req models.VideoListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.backendClient.GetVideoList(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 过滤已下载的视频
	if h.syncService != nil {
		response.Videos = h.syncService.FilterRemoteVideos(response.Videos)
		response.Total = int64(len(response.Videos))

		// 重新分页
		start := (req.Page - 1) * req.PageSize
		end := start + req.PageSize
		if start >= len(response.Videos) {
			response.Videos = []models.BackupVideoInfo{}
		} else {
			if end > len(response.Videos) {
				end = len(response.Videos)
			}
			response.Videos = response.Videos[start:end]
		}
		response.TotalPages = int((response.Total + int64(req.PageSize) - 1) / int64(req.PageSize))
	}

	c.JSON(http.StatusOK, response)
}

// DownloadVideos 下载视频
func (h *Handler) DownloadVideos(c *gin.Context) {
	var req models.DownloadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.VideoIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID列表不能为空"})
		return
	}

	// 获取视频详细信息
	var taskIDs []string
	for _, videoID := range req.VideoIDs {
		// 这里需要根据videoID获取完整的视频信息
		// 为了简化，我们先获取第一页的视频列表，然后匹配
		videoListReq := &models.VideoListRequest{
			Page:     1,
			PageSize: 100, // 获取更多数据以便匹配
		}
		
		response, err := h.backendClient.GetVideoList(videoListReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 查找匹配的视频
		var foundVideo *models.BackupVideoInfo
		for _, video := range response.Videos {
			if video.VideoID == videoID {
				foundVideo = &video
				break
			}
		}

		if foundVideo == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "视频不存在: " + videoID})
			return
		}

		// 添加下载任务
		taskID := h.downloadService.AddDownloadTask(*foundVideo)
		taskIDs = append(taskIDs, taskID)
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "下载任务已创建",
		"task_ids": taskIDs,
	})
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	task, exists := h.downloadService.GetTask(taskID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetAllTasks 获取所有任务
func (h *Handler) GetAllTasks(c *gin.Context) {
	tasks := h.downloadService.GetAllTasks()
	c.JSON(http.StatusOK, models.TaskStatusResponse{
		Tasks: tasks,
		Total: len(tasks),
	})
}

// CancelTask 取消任务
func (h *Handler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.CancelTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已取消"})
}

// GetLocalVideos 获取本地视频列表
func (h *Handler) GetLocalVideos(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetLocalVideos(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
	})
}

// GetStorageStats 获取存储统计
func (h *Handler) GetStorageStats(c *gin.Context) {
	stats, err := h.storageService.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// DeleteVideo 删除本地视频
func (h *Handler) DeleteVideo(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID不能为空"})
		return
	}

	err := h.storageService.DeleteVideo(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "视频已删除"})
}

// CleanupStorage 清理存储
func (h *Handler) CleanupStorage(c *gin.Context) {
	err := h.storageService.CleanupEmptyDirs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "存储清理完成"})
}

// TestConnection 测试后端连接
func (h *Handler) TestConnection(c *gin.Context) {
	err := h.backendClient.TestConnection()
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"message": "后端连接正常",
	})
}

// Health 健康检查
func (h *Handler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"service": "backup_server",
	})
}

// ==================== 同步相关处理方法 ====================

// GetSyncSummary 获取同步摘要
func (h *Handler) GetSyncSummary(c *gin.Context) {
	summary, err := h.syncService.GetSyncSummary()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetFilteredVideos 获取筛选后的视频列表
func (h *Handler) GetFilteredVideos(c *gin.Context) {
	var req models.VideoFilterRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.Filter == "" {
		req.Filter = models.FilterAll
	}

	videos, total, err := h.syncService.GetFilteredVideos(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        req.Page,
		"page_size":   req.PageSize,
		"total_pages": (total + req.PageSize - 1) / req.PageSize,
		"filter":      req.Filter,
	})
}

// RefreshSync 刷新同步数据库
func (h *Handler) RefreshSync(c *gin.Context) {
	err := h.syncService.RefreshSyncDatabase()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "同步数据库已刷新"})
}

// ==================== 分类管理相关处理方法 ====================

// GetVideosByDevice 按设备获取视频列表
func (h *Handler) GetVideosByDevice(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetVideosByDevice(deviceID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
		"device_id":   deviceID,
	})
}

// GetVideosByCat 按猫获取视频列表
func (h *Handler) GetVideosByCat(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "猫ID不能为空"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetVideosByCat(catID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
		"cat_id":      catID,
	})
}

// GetDeviceList 获取设备列表
func (h *Handler) GetDeviceList(c *gin.Context) {
	devices, err := h.storageService.GetDeviceList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"devices": devices,
		"count":   len(devices),
	})
}

// GetCatList 获取猫列表
func (h *Handler) GetCatList(c *gin.Context) {
	cats, err := h.storageService.GetCatList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"cats":  cats,
		"count": len(cats),
	})
}

// GetDeviceStats 获取设备统计
func (h *Handler) GetDeviceStats(c *gin.Context) {
	stats, err := h.storageService.GetDeviceStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetCatStats 获取猫统计
func (h *Handler) GetCatStats(c *gin.Context) {
	stats, err := h.storageService.GetCatStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}
