package services

import (
	"backup_server/pkg/config"
	"backup_server/pkg/models"
	"backup_server/pkg/utils"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// StorageService 存储服务
type StorageService struct {
	config      *config.Config
	syncService *SyncService // 添加同步服务引用
}

// NewStorageService 创建存储服务
func NewStorageService(cfg *config.Config) *StorageService {
	return &StorageService{
		config: cfg,
	}
}

// SetSyncService 设置同步服务
func (s *StorageService) SetSyncService(syncService *SyncService) {
	s.syncService = syncService
}

// GetLocalVideos 获取本地视频列表
func (s *StorageService) GetLocalVideos(page, pageSize int) ([]models.LocalVideoInfo, int, error) {
	var videos []models.LocalVideoInfo
	
	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device")
	if !utils.FileExists(deviceDir) {
		return videos, 0, nil
	}

	// 遍历设备目录
	err := filepath.Walk(deviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找metadata.json文件
		if info.Name() == "metadata.json" {
			video, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil // 跳过错误的元数据文件
			}
			videos = append(videos, *video)
		}
		return nil
	})

	if err != nil {
		return nil, 0, fmt.Errorf("遍历本地视频失败: %v", err)
	}

	// 分页
	total := len(videos)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		return []models.LocalVideoInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return videos[start:end], total, nil
}

// parseVideoMetadata 解析视频元数据
func (s *StorageService) parseVideoMetadata(metadataPath string) (*models.LocalVideoInfo, error) {
	file, err := os.Open(metadataPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var metadata map[string]interface{}
	if err := json.NewDecoder(file).Decode(&metadata); err != nil {
		return nil, err
	}

	// 获取视频目录路径
	videoDir := filepath.Dir(metadataPath)
	
	// 计算目录大小和文件数量
	dirSize, _ := utils.GetDirSize(videoDir)
	fileCount, _ := utils.CountFiles(videoDir)

	// 解析下载时间
	downloadedAt := time.Now()
	if downloadedAtStr, ok := metadata["downloaded_at"].(string); ok {
		if t, err := time.Parse("2006-01-02 15:04:05", downloadedAtStr); err == nil {
			downloadedAt = t
		}
	}

	video := &models.LocalVideoInfo{
		VideoID:      getString(metadata, "video_id"),
		DeviceID:     getString(metadata, "device_id"),
		AnimalID:     getString(metadata, "animal_id"),
		StartTime:    getInt64(metadata, "start_time"),
		LocalPath:    videoDir,
		DownloadedAt: downloadedAt,
		FileSize:     dirSize,
		FileCount:    fileCount,
	}

	return video, nil
}

// GetStorageStats 获取存储统计
func (s *StorageService) GetStorageStats() (*models.StorageStats, error) {
	stats := &models.StorageStats{
		LastUpdated: time.Now(),
	}

	dataPath := s.config.Storage.DataPath
	if !utils.FileExists(dataPath) {
		return stats, nil
	}

	// 统计设备数量
	deviceDir := filepath.Join(dataPath, "by_device")
	if utils.FileExists(deviceDir) {
		devices, err := os.ReadDir(deviceDir)
		if err == nil {
			stats.DeviceCount = len(devices)
		}
	}

	// 统计猫数量
	catDir := filepath.Join(dataPath, "by_cat")
	if utils.FileExists(catDir) {
		cats, err := os.ReadDir(catDir)
		if err == nil {
			stats.CatCount = len(cats)
		}
	}

	// 统计视频和文件
	err := filepath.Walk(dataPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.Name() == "metadata.json" {
			stats.TotalVideos++
		} else if !info.IsDir() && !strings.HasSuffix(info.Name(), ".json") {
			stats.TotalFiles++
			stats.TotalSize += info.Size()
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("统计存储信息失败: %v", err)
	}

	return stats, nil
}

// DeleteVideo 删除本地视频
func (s *StorageService) DeleteVideo(videoID string) error {
	// 查找视频路径
	videoPath, err := s.findVideoPath(videoID)
	if err != nil {
		return err
	}

	// 删除设备目录下的视频
	if err := os.RemoveAll(videoPath); err != nil {
		return fmt.Errorf("删除视频文件失败: %v", err)
	}

	// 删除猫分类下的符号链接
	if err := s.deleteCatSymlink(videoID); err != nil {
		// 符号链接删除失败不影响主要操作
		fmt.Printf("删除猫分类符号链接失败: %v\n", err)
	}

	// 从同步数据库中移除记录
	if s.syncService != nil {
		if err := s.syncService.RemoveDownloadRecord(videoID); err != nil {
			fmt.Printf("移除同步记录失败: %v\n", err)
		}
	}

	return nil
}

// findVideoPath 查找视频路径
func (s *StorageService) findVideoPath(videoID string) (string, error) {
	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device")
	
	var foundPath string
	err := filepath.Walk(deviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.Name() == "metadata.json" {
			metadata, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil
			}
			if metadata.VideoID == videoID {
				foundPath = filepath.Dir(path)
				return filepath.SkipDir
			}
		}
		return nil
	})

	if err != nil {
		return "", err
	}
	if foundPath == "" {
		return "", fmt.Errorf("视频不存在")
	}

	return foundPath, nil
}

// deleteCatSymlink 删除猫分类符号链接
func (s *StorageService) deleteCatSymlink(videoID string) error {
	catDir := filepath.Join(s.config.Storage.DataPath, "by_cat")
	if !utils.FileExists(catDir) {
		return nil
	}

	return filepath.Walk(catDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查是否是符号链接
		if info.Mode()&os.ModeSymlink != 0 {
			// 检查符号链接指向的目录是否包含对应的videoID
			target, err := os.Readlink(path)
			if err != nil {
				return nil
			}

			metadataPath := filepath.Join(target, "metadata.json")
			if utils.FileExists(metadataPath) {
				metadata, err := s.parseVideoMetadata(metadataPath)
				if err != nil {
					return nil
				}
				if metadata.VideoID == videoID {
					return os.Remove(path)
				}
			}
		}
		return nil
	})
}

// CleanupEmptyDirs 清理空目录
func (s *StorageService) CleanupEmptyDirs() error {
	dataPath := s.config.Storage.DataPath
	
	return filepath.Walk(dataPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() && path != dataPath {
			// 检查目录是否为空
			entries, err := os.ReadDir(path)
			if err != nil {
				return nil
			}
			if len(entries) == 0 {
				os.Remove(path)
			}
		}
		return nil
	})
}

// 辅助函数
func getString(m map[string]interface{}, key string) string {
	if v, ok := m[key].(string); ok {
		return v
	}
	return ""
}

func getInt64(m map[string]interface{}, key string) int64 {
	if v, ok := m[key].(float64); ok {
		return int64(v)
	}
	return 0
}

// GetVideosByDevice 按设备获取视频列表
func (s *StorageService) GetVideosByDevice(deviceID string, page, pageSize int) ([]models.LocalVideoInfo, int, error) {
	var videos []models.LocalVideoInfo

	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device", deviceID)
	if !utils.FileExists(deviceDir) {
		return videos, 0, nil
	}

	// 遍历设备目录
	err := filepath.Walk(deviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找metadata.json文件
		if info.Name() == "metadata.json" {
			video, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil // 跳过错误的元数据文件
			}
			videos = append(videos, *video)
		}
		return nil
	})

	if err != nil {
		return nil, 0, fmt.Errorf("遍历设备视频失败: %v", err)
	}

	// 分页
	total := len(videos)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		return []models.LocalVideoInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return videos[start:end], total, nil
}

// GetVideosByCat 按猫获取视频列表
func (s *StorageService) GetVideosByCat(catID string, page, pageSize int) ([]models.LocalVideoInfo, int, error) {
	var videos []models.LocalVideoInfo

	catDir := filepath.Join(s.config.Storage.DataPath, "by_cat", catID)
	if !utils.FileExists(catDir) {
		return videos, 0, nil
	}

	// 遍历猫目录
	err := filepath.Walk(catDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找metadata.json文件
		if info.Name() == "metadata.json" {
			video, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil // 跳过错误的元数据文件
			}
			videos = append(videos, *video)
		}
		return nil
	})

	if err != nil {
		return nil, 0, fmt.Errorf("遍历猫视频失败: %v", err)
	}

	// 分页
	total := len(videos)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		return []models.LocalVideoInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return videos[start:end], total, nil
}

// GetDeviceList 获取设备列表
func (s *StorageService) GetDeviceList() ([]string, error) {
	var devices []string

	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device")
	if !utils.FileExists(deviceDir) {
		return devices, nil
	}

	entries, err := os.ReadDir(deviceDir)
	if err != nil {
		return nil, fmt.Errorf("读取设备目录失败: %v", err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			devices = append(devices, entry.Name())
		}
	}

	return devices, nil
}

// GetCatList 获取猫列表
func (s *StorageService) GetCatList() ([]string, error) {
	var cats []string

	catDir := filepath.Join(s.config.Storage.DataPath, "by_cat")
	if !utils.FileExists(catDir) {
		return cats, nil
	}

	entries, err := os.ReadDir(catDir)
	if err != nil {
		return nil, fmt.Errorf("读取猫目录失败: %v", err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			cats = append(cats, entry.Name())
		}
	}

	return cats, nil
}

// GetDeviceStats 获取设备统计信息
func (s *StorageService) GetDeviceStats() (map[string]interface{}, error) {
	devices, err := s.GetDeviceList()
	if err != nil {
		return nil, err
	}

	stats := make(map[string]interface{})

	for _, deviceID := range devices {
		videos, total, err := s.GetVideosByDevice(deviceID, 1, 1000)
		if err != nil {
			continue
		}

		var totalSize int64
		for _, video := range videos {
			totalSize += video.FileSize
		}

		stats[deviceID] = map[string]interface{}{
			"video_count": total,
			"total_size":  totalSize,
		}
	}

	return stats, nil
}

// GetCatStats 获取猫统计信息
func (s *StorageService) GetCatStats() (map[string]interface{}, error) {
	cats, err := s.GetCatList()
	if err != nil {
		return nil, err
	}

	stats := make(map[string]interface{})

	for _, catID := range cats {
		videos, total, err := s.GetVideosByCat(catID, 1, 1000)
		if err != nil {
			continue
		}

		var totalSize int64
		for _, video := range videos {
			totalSize += video.FileSize
		}

		stats[catID] = map[string]interface{}{
			"video_count": total,
			"total_size":  totalSize,
		}
	}

	return stats, nil
}
