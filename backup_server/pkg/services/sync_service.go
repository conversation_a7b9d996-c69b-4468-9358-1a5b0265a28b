package services

import (
	"backup_server/pkg/config"
	"backup_server/pkg/models"
	"backup_server/pkg/utils"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// SyncService 同步服务
type SyncService struct {
	config         *config.Config
	backendClient  *BackendClient
	storageService *StorageService
	syncDBPath     string
	syncDB         *models.SyncDatabase
}

// NewSyncService 创建同步服务
func NewSyncService(cfg *config.Config, backendClient *BackendClient, storageService *StorageService) *SyncService {
	syncDBPath := filepath.Join(cfg.Storage.DataPath, "sync_database.json")
	
	service := &SyncService{
		config:         cfg,
		backendClient:  backendClient,
		storageService: storageService,
		syncDBPath:     syncDBPath,
	}
	
	// 加载同步数据库
	if err := service.loadSyncDatabase(); err != nil {
		// 如果加载失败，创建新的同步数据库
		service.syncDB = &models.SyncDatabase{
			Version:    "1.0",
			LastSync:   time.Now(),
			Records:    make(map[string]models.SyncRecord),
			DeletedIDs: []string{},
			TotalCount: 0,
			TotalSize:  0,
		}
	}
	
	return service
}

// loadSyncDatabase 加载同步数据库
func (s *SyncService) loadSyncDatabase() error {
	if !utils.FileExists(s.syncDBPath) {
		return fmt.Errorf("同步数据库文件不存在")
	}
	
	data, err := os.ReadFile(s.syncDBPath)
	if err != nil {
		return fmt.Errorf("读取同步数据库失败: %v", err)
	}
	
	var syncDB models.SyncDatabase
	if err := json.Unmarshal(data, &syncDB); err != nil {
		return fmt.Errorf("解析同步数据库失败: %v", err)
	}
	
	s.syncDB = &syncDB
	return nil
}

// saveSyncDatabase 保存同步数据库
func (s *SyncService) saveSyncDatabase() error {
	// 确保目录存在
	if err := utils.EnsureDir(filepath.Dir(s.syncDBPath)); err != nil {
		return fmt.Errorf("创建同步数据库目录失败: %v", err)
	}
	
	// 更新统计信息
	s.syncDB.TotalCount = len(s.syncDB.Records)
	s.syncDB.TotalSize = 0
	for _, record := range s.syncDB.Records {
		if record.Status == "downloaded" {
			s.syncDB.TotalSize += record.FileSize
		}
	}
	s.syncDB.LastSync = time.Now()
	
	data, err := json.MarshalIndent(s.syncDB, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化同步数据库失败: %v", err)
	}
	
	if err := os.WriteFile(s.syncDBPath, data, 0644); err != nil {
		return fmt.Errorf("保存同步数据库失败: %v", err)
	}
	
	return nil
}

// AddDownloadRecord 添加下载记录
func (s *SyncService) AddDownloadRecord(videoInfo models.BackupVideoInfo, localPath string) error {
	// 计算文件大小和数量
	fileSize, _ := utils.GetDirSize(localPath)
	fileCount, _ := utils.CountFiles(localPath)
	
	// 计算校验和（可选）
	checksum := ""
	metadataPath := filepath.Join(localPath, "metadata.json")
	if utils.FileExists(metadataPath) {
		checksum, _ = utils.CalculateMD5(metadataPath)
	}
	
	record := models.SyncRecord{
		VideoID:      videoInfo.VideoID,
		DeviceID:     videoInfo.DeviceID,
		AnimalID:     videoInfo.AnimalID,
		StartTime:    videoInfo.StartTime,
		DownloadedAt: time.Now(),
		LocalPath:    localPath,
		FileSize:     fileSize,
		FileCount:    fileCount,
		Status:       "downloaded",
		Checksum:     checksum,
	}
	
	s.syncDB.Records[videoInfo.VideoID] = record
	return s.saveSyncDatabase()
}

// RemoveDownloadRecord 移除下载记录
func (s *SyncService) RemoveDownloadRecord(videoID string) error {
	if record, exists := s.syncDB.Records[videoID]; exists {
		// 标记为已删除，而不是直接删除记录
		record.Status = "deleted"
		s.syncDB.Records[videoID] = record
		
		// 添加到已删除列表
		s.syncDB.DeletedIDs = append(s.syncDB.DeletedIDs, videoID)
	}
	
	return s.saveSyncDatabase()
}

// IsVideoDownloaded 检查视频是否已下载
func (s *SyncService) IsVideoDownloaded(videoID string) bool {
	record, exists := s.syncDB.Records[videoID]
	return exists && record.Status == "downloaded"
}

// GetDownloadedVideoIDs 获取已下载的视频ID列表
func (s *SyncService) GetDownloadedVideoIDs() []string {
	var ids []string
	for videoID, record := range s.syncDB.Records {
		if record.Status == "downloaded" {
			ids = append(ids, videoID)
		}
	}
	return ids
}

// FilterRemoteVideos 过滤远程视频，移除已下载的
func (s *SyncService) FilterRemoteVideos(remoteVideos []models.BackupVideoInfo) []models.BackupVideoInfo {
	var filtered []models.BackupVideoInfo
	
	for _, video := range remoteVideos {
		if !s.IsVideoDownloaded(video.VideoID) {
			filtered = append(filtered, video)
		}
	}
	
	return filtered
}

// GetSyncSummary 获取同步摘要
func (s *SyncService) GetSyncSummary() (*models.SyncSummary, error) {
	// 获取远程视频列表
	remoteReq := &models.VideoListRequest{
		Page:     1,
		PageSize: 1000, // 获取大量数据进行比较
	}
	
	remoteResp, err := s.backendClient.GetVideoList(remoteReq)
	if err != nil {
		return nil, fmt.Errorf("获取远程视频列表失败: %v", err)
	}
	
	// 获取本地视频列表
	localVideos, _, err := s.storageService.GetLocalVideos(1, 1000)
	if err != nil {
		return nil, fmt.Errorf("获取本地视频列表失败: %v", err)
	}
	
	// 创建映射以便快速查找
	remoteMap := make(map[string]models.BackupVideoInfo)
	for _, video := range remoteResp.Videos {
		remoteMap[video.VideoID] = video
	}
	
	localMap := make(map[string]models.LocalVideoInfo)
	for _, video := range localVideos {
		localMap[video.VideoID] = video
	}
	
	// 分析同步状态
	var videoStatuses []models.VideoSyncStatus
	synced := 0
	localOnly := 0
	remoteOnly := 0
	conflicts := 0
	
	// 检查所有远程视频
	for videoID := range remoteMap {
		status := models.VideoSyncStatus{
			VideoID:      videoID,
			RemoteExists: true,
		}
		
		if localVideo, exists := localMap[videoID]; exists {
			status.LocalExists = true
			status.LocalFileSize = localVideo.FileSize
			status.LastSyncTime = &localVideo.DownloadedAt
			
			// 检查是否同步
			if record, recordExists := s.syncDB.Records[videoID]; recordExists && record.Status == "downloaded" {
				status.SyncStatus = "synced"
				synced++
			} else {
				status.SyncStatus = "conflict"
				conflicts++
			}
		} else {
			status.LocalExists = false
			status.SyncStatus = "remote_only"
			remoteOnly++
		}
		
		videoStatuses = append(videoStatuses, status)
	}
	
	// 检查仅本地存在的视频
	for videoID, localVideo := range localMap {
		if _, exists := remoteMap[videoID]; !exists {
			status := models.VideoSyncStatus{
				VideoID:       videoID,
				LocalExists:   true,
				RemoteExists:  false,
				SyncStatus:    "local_only",
				LocalFileSize: localVideo.FileSize,
				LastSyncTime:  &localVideo.DownloadedAt,
			}
			videoStatuses = append(videoStatuses, status)
			localOnly++
		}
	}
	
	summary := &models.SyncSummary{
		TotalRemote:   len(remoteResp.Videos),
		TotalLocal:    len(localVideos),
		Synced:        synced,
		LocalOnly:     localOnly,
		RemoteOnly:    remoteOnly,
		Conflicts:     conflicts,
		LastSyncTime:  s.syncDB.LastSync,
		VideoStatuses: videoStatuses,
	}
	
	return summary, nil
}

// GetFilteredVideos 获取筛选后的视频列表
func (s *SyncService) GetFilteredVideos(req *models.VideoFilterRequest) ([]models.VideoSyncStatus, int, error) {
	summary, err := s.GetSyncSummary()
	if err != nil {
		return nil, 0, err
	}
	
	// 根据筛选条件过滤
	var filtered []models.VideoSyncStatus
	for _, status := range summary.VideoStatuses {
		// 应用筛选条件
		if req.Filter != models.FilterAll {
			switch req.Filter {
			case models.FilterSynced:
				if status.SyncStatus != "synced" {
					continue
				}
			case models.FilterLocalOnly:
				if status.SyncStatus != "local_only" {
					continue
				}
			case models.FilterRemoteOnly:
				if status.SyncStatus != "remote_only" {
					continue
				}
			case models.FilterConflicts:
				if status.SyncStatus != "conflict" {
					continue
				}
			}
		}
		
		// 应用设备ID筛选
		if req.DeviceID != nil {
			// 需要从远程或本地数据中获取设备ID信息
			// 这里简化处理，实际应用中可能需要更复杂的逻辑
		}
		
		// 应用猫ID筛选
		if req.AnimalID != nil {
			// 同样需要从数据中获取猫ID信息
		}
		
		filtered = append(filtered, status)
	}
	
	// 分页
	total := len(filtered)
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start >= total {
		return []models.VideoSyncStatus{}, total, nil
	}
	if end > total {
		end = total
	}
	
	return filtered[start:end], total, nil
}

// GetSyncDatabase 获取同步数据库（用于调试）
func (s *SyncService) GetSyncDatabase() *models.SyncDatabase {
	return s.syncDB
}

// RefreshSyncDatabase 刷新同步数据库
func (s *SyncService) RefreshSyncDatabase() error {
	// 重新扫描本地文件，更新同步数据库
	localVideos, _, err := s.storageService.GetLocalVideos(1, 1000)
	if err != nil {
		return fmt.Errorf("获取本地视频失败: %v", err)
	}
	
	// 更新记录
	for _, video := range localVideos {
		if record, exists := s.syncDB.Records[video.VideoID]; exists {
			// 更新现有记录
			record.FileSize = video.FileSize
			record.FileCount = video.FileCount
			record.LocalPath = video.LocalPath
			if record.Status == "deleted" {
				record.Status = "downloaded" // 恢复状态
			}
			s.syncDB.Records[video.VideoID] = record
		} else {
			// 创建新记录
			record := models.SyncRecord{
				VideoID:      video.VideoID,
				DeviceID:     video.DeviceID,
				AnimalID:     video.AnimalID,
				StartTime:    video.StartTime,
				DownloadedAt: video.DownloadedAt,
				LocalPath:    video.LocalPath,
				FileSize:     video.FileSize,
				FileCount:    video.FileCount,
				Status:       "downloaded",
			}
			s.syncDB.Records[video.VideoID] = record
		}
	}
	
	return s.saveSyncDatabase()
}
