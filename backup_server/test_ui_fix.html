<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地视频页面修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .video-grid {
            display: grid;
            gap: 15px;
        }
        
        .video-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        
        .video-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* 本地视频卡片样式 */
        .local-video-card {
            max-width: 100%;
            margin-bottom: 15px;
        }
        
        .local-video-card .video-header {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .local-video-card .video-id {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .local-video-card .video-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .local-video-card .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .local-video-card .detail-row strong {
            min-width: 80px;
            font-size: 13px;
            color: #2c3e50;
        }
        
        .local-video-card .detail-row span {
            font-size: 13px;
            color: #495057;
            text-align: right;
            flex: 1;
            margin-left: 10px;
        }
        
        .local-video-card .video-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 8px;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            .local-video-card .video-details {
                grid-template-columns: 1fr;
            }
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频备份系统 - UI修复测试</h1>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('local')">本地视频</button>
            <button class="tab" onclick="switchTab('category')">分类查看</button>
        </div>
        
        <!-- 本地视频标签页 -->
        <div class="tab-pane active" id="local-pane">
            <!-- 普通列表视图 -->
            <div id="normal-view">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadLocalVideos()">刷新列表</button>
                    <button class="btn btn-primary" onclick="showCategoryView()">分类查看</button>
                    <button class="btn btn-danger" onclick="cleanupStorage()">清理存储</button>
                </div>
                <div id="local-videos" class="video-grid">
                    <!-- 模拟本地视频数据 -->
                    <div class="video-card local-video-card">
                        <div class="video-info">
                            <div class="video-header">
                                <strong>视频ID:</strong> <span class="video-id">93c2531d58f3ce1b6</span>
                            </div>
                            <div class="video-details">
                                <div class="detail-row">
                                    <strong>设备ID:</strong> <span>202502270220f7cbb</span>
                                </div>
                                <div class="detail-row">
                                    <strong>猫ID:</strong> <span>f3ce1b02b40e9477</span>
                                </div>
                                <div class="detail-row">
                                    <strong>开始时间:</strong> <span>2025/7/30 11:46:35</span>
                                </div>
                                <div class="detail-row">
                                    <strong>文件大小:</strong> <span>32.63 MB</span>
                                </div>
                                <div class="detail-row">
                                    <strong>文件数量:</strong> <span>23</span>
                                </div>
                                <div class="detail-row">
                                    <strong>下载时间:</strong> <span>2025/7/30 13:30:15</span>
                                </div>
                            </div>
                        </div>
                        <div class="video-actions">
                            <button class="btn btn-danger" onclick="deleteVideo('93c2531d58f3ce1b6')">删除</button>
                        </div>
                    </div>
                    
                    <div class="video-card local-video-card">
                        <div class="video-info">
                            <div class="video-header">
                                <strong>视频ID:</strong> <span class="video-id">93c2531d58f3ce1b7</span>
                            </div>
                            <div class="video-details">
                                <div class="detail-row">
                                    <strong>设备ID:</strong> <span>202502270220f7cbb</span>
                                </div>
                                <div class="detail-row">
                                    <strong>猫ID:</strong> <span>f3ce1b02b40ed223</span>
                                </div>
                                <div class="detail-row">
                                    <strong>开始时间:</strong> <span>2025/7/30 13:13:05</span>
                                </div>
                                <div class="detail-row">
                                    <strong>文件大小:</strong> <span>95.76 MB</span>
                                </div>
                                <div class="detail-row">
                                    <strong>文件数量:</strong> <span>50</span>
                                </div>
                                <div class="detail-row">
                                    <strong>下载时间:</strong> <span>2025/7/30 14:05:22</span>
                                </div>
                            </div>
                        </div>
                        <div class="video-actions">
                            <button class="btn btn-danger" onclick="deleteVideo('93c2531d58f3ce1b7')">删除</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类查看区域 -->
            <div id="category-view" style="display: none;">
                <div class="controls">
                    <button class="btn btn-primary" onclick="showDeviceView()">按设备查看</button>
                    <button class="btn btn-primary" onclick="showCatView()">按猫查看</button>
                    <button class="btn btn-primary" onclick="hideCategoryView()">返回列表</button>
                </div>
                
                <div id="device-view" style="display: none;">
                    <h3 style="margin: 20px 0 15px 0; color: #2c3e50;">按设备分类</h3>
                    <div class="loading">设备分类功能正常</div>
                </div>
                
                <div id="cat-view" style="display: none;">
                    <h3 style="margin: 20px 0 15px 0; color: #2c3e50;">按猫分类</h3>
                    <div class="loading">猫分类功能正常</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tabName + '-pane').classList.add('active');
        }
        
        // 显示分类视图
        function showCategoryView() {
            document.getElementById('normal-view').style.display = 'none';
            document.getElementById('category-view').style.display = 'block';
        }
        
        // 隐藏分类视图
        function hideCategoryView() {
            document.getElementById('category-view').style.display = 'none';
            document.getElementById('device-view').style.display = 'none';
            document.getElementById('cat-view').style.display = 'none';
            document.getElementById('normal-view').style.display = 'block';
        }
        
        // 显示设备视图
        function showDeviceView() {
            document.getElementById('device-view').style.display = 'block';
            document.getElementById('cat-view').style.display = 'none';
        }
        
        // 显示猫视图
        function showCatView() {
            document.getElementById('cat-view').style.display = 'block';
            document.getElementById('device-view').style.display = 'none';
        }
        
        // 模拟函数
        function loadLocalVideos() {
            alert('刷新本地视频列表');
        }
        
        function cleanupStorage() {
            alert('清理存储');
        }
        
        function deleteVideo(videoId) {
            alert('删除视频: ' + videoId);
        }
    </script>
</body>
</html>
