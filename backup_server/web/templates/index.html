<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 400px;
        }
        
        .tab-pane {
            display: none;
            padding: 20px;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .video-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        
        .video-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .video-card.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .video-info {
            margin-bottom: 10px;
        }
        
        .video-info strong {
            color: #2c3e50;
        }

        /* 本地视频卡片样式 */
        .local-video-card {
            max-width: 100%;
            margin-bottom: 15px;
        }

        .local-video-card .video-header {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .local-video-card .video-id {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .local-video-card .video-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }

        .local-video-card .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }

        .local-video-card .detail-row strong {
            min-width: 80px;
            font-size: 13px;
        }

        .local-video-card .detail-row span {
            font-size: 13px;
            color: #495057;
            text-align: right;
            flex: 1;
            margin-left: 10px;
        }

        .local-video-card .video-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 8px;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .local-video-card .video-details {
                grid-template-columns: 1fr;
            }
        }
        
        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .task-info {
            flex: 1;
        }
        
        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending { background: #f39c12; color: white; }
        .status-downloading { background: #3498db; color: white; }
        .status-completed { background: #27ae60; color: white; }
        .status-failed { background: #e74c3c; color: white; }
        .status-cancelled { background: #95a5a6; color: white; }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 5px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .sync-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .sync-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #3498db;
        }

        .sync-card.synced { border-left-color: #27ae60; }
        .sync-card.remote-only { border-left-color: #f39c12; }
        .sync-card.local-only { border-left-color: #9b59b6; }
        .sync-card.conflicts { border-left-color: #e74c3c; }

        .sync-card h4 {
            color: #7f8c8d;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .sync-card .count {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }

        .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .category-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .category-item:hover {
            background: #e9ecef;
            border-color: #3498db;
        }

        .category-item.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .category-item .name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .category-item .stats {
            font-size: 12px;
            color: #6c757d;
        }

        .category-item.active .stats {
            color: rgba(255,255,255,0.8);
        }

        .sync-status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }

        .sync-status.synced { background: #d4edda; color: #155724; }
        .sync-status.remote-only { background: #fff3cd; color: #856404; }
        .sync-status.local-only { background: #e2e3f0; color: #383d41; }
        .sync-status.conflict { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.title}}</h1>
            <p>管理和下载猫砂盆视频数据</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>本地视频</h3>
                <div class="value" id="local-videos">-</div>
            </div>
            <div class="stat-card">
                <h3>存储大小</h3>
                <div class="value" id="storage-size">-</div>
            </div>
            <div class="stat-card">
                <h3>设备数量</h3>
                <div class="value" id="device-count">-</div>
            </div>
            <div class="stat-card">
                <h3>猫咪数量</h3>
                <div class="value" id="cat-count">-</div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('remote')">云端视频</div>
            <div class="tab" onclick="switchTab('local')">本地视频</div>
            <div class="tab" onclick="switchTab('sync')">同步管理</div>
            <div class="tab" onclick="switchTab('tasks')">下载任务</div>
        </div>
        
        <div class="tab-content">
            <!-- 云端视频 -->
            <div class="tab-pane active" id="remote-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadRemoteVideos()">刷新列表</button>
                    <button class="btn btn-success" onclick="downloadSelected()" id="download-btn" disabled>下载选中</button>
                    <button class="btn btn-primary" onclick="selectAll()">全选</button>
                    <button class="btn btn-primary" onclick="clearSelection()">清空选择</button>
                </div>

                <!-- 筛选提示 -->
                <div class="success" id="filter-info">
                    <strong>智能筛选:</strong> 已自动过滤掉本地已下载的视频，仅显示云端独有的视频。
                    如需查看所有视频的同步状态，请切换到"同步管理"标签页。
                </div>

                <div id="remote-videos" class="video-grid"></div>
                <div class="pagination" id="remote-pagination"></div>
            </div>
            
            <!-- 本地视频 -->
            <div class="tab-pane" id="local-pane">
                <!-- 普通列表视图 -->
                <div id="normal-view">
                    <div class="controls">
                        <button class="btn btn-primary" onclick="loadLocalVideos()">刷新列表</button>
                        <button class="btn btn-primary" onclick="showCategoryView()">分类查看</button>
                        <button class="btn btn-danger" onclick="cleanupStorage()">清理存储</button>
                    </div>
                    <div id="local-videos" class="video-grid"></div>
                    <div class="pagination" id="local-pagination"></div>
                </div>

                <!-- 分类查看区域 -->
                <div id="category-view" style="display: none;">
                    <div class="controls">
                        <button class="btn btn-primary" onclick="showDeviceView()">按设备查看</button>
                        <button class="btn btn-primary" onclick="showCatView()">按猫查看</button>
                        <button class="btn btn-primary" onclick="hideCategoryView()">返回列表</button>
                    </div>

                    <div id="device-view" style="display: none;">
                        <h3 style="margin: 20px 0 15px 0; color: #2c3e50;">按设备分类</h3>
                        <div id="device-list" class="category-list"></div>
                        <div id="device-videos" class="video-grid"></div>
                        <div class="pagination" id="device-pagination"></div>
                    </div>

                    <div id="cat-view" style="display: none;">
                        <h3 style="margin: 20px 0 15px 0; color: #2c3e50;">按猫分类</h3>
                        <div id="cat-list" class="category-list"></div>
                        <div id="cat-videos" class="video-grid"></div>
                        <div class="pagination" id="cat-pagination"></div>
                    </div>
                </div>
            </div>

            <!-- 同步管理 -->
            <div class="tab-pane" id="sync-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadSyncSummary()">刷新同步状态</button>
                    <button class="btn btn-success" onclick="refreshSyncDatabase()">刷新同步数据库</button>
                </div>

                <!-- 同步摘要 -->
                <div id="sync-summary" class="sync-summary"></div>

                <!-- 筛选控件 -->
                <div class="controls">
                    <select id="sync-filter" onchange="loadFilteredVideos()">
                        <option value="all">全部视频</option>
                        <option value="synced">已同步</option>
                        <option value="remote_only">仅云端存在</option>
                        <option value="local_only">仅本地存在</option>
                        <option value="conflicts">冲突</option>
                    </select>
                </div>

                <div id="sync-videos" class="video-grid"></div>
                <div class="pagination" id="sync-pagination"></div>
            </div>
            
            <!-- 下载任务 -->
            <div class="tab-pane" id="tasks-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadTasks()">刷新任务</button>
                </div>
                <div id="tasks" class="task-list"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedVideos = new Set();
        let currentPage = 1;
        let pageSize = 20;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadRemoteVideos();

            // 定时刷新任务状态
            setInterval(loadTasks, 5000);
        });

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            event.target.classList.add('active');
            document.getElementById(tabName + '-pane').classList.add('active');

            // 加载对应数据
            if (tabName === 'remote') {
                loadRemoteVideos();
            } else if (tabName === 'local') {
                // 确保显示普通视图
                document.getElementById('normal-view').style.display = 'block';
                document.getElementById('category-view').style.display = 'none';
                loadLocalVideos();
                hideCategoryView(); // 隐藏分类视图
            } else if (tabName === 'sync') {
                loadSyncSummary();
            } else if (tabName === 'tasks') {
                loadTasks();
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/storage/stats');
                const stats = await response.json();

                document.getElementById('local-videos').textContent = stats.total_videos || 0;
                document.getElementById('storage-size').textContent = formatBytes(stats.total_size || 0);
                document.getElementById('device-count').textContent = stats.device_count || 0;
                document.getElementById('cat-count').textContent = stats.cat_count || 0;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载云端视频列表
        async function loadRemoteVideos(page = 1) {
            const container = document.getElementById('remote-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/remote?page=${page}&page_size=${pageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderVideoGrid(data.videos, container, 'remote');
                renderPagination(data.page, data.total_pages, 'remote-pagination', loadRemoteVideos);

                currentPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 加载本地视频列表
        async function loadLocalVideos(page = 1) {
            const container = document.getElementById('local-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/local?page=${page}&page_size=${pageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'local-pagination', loadLocalVideos);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染视频网格
        function renderVideoGrid(videos, container, type) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card ${selectedVideos.has(video.video_id) ? 'selected' : ''}"
                     onclick="toggleVideoSelection('${video.video_id}')">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>猫ID:</strong> ${video.animal_id || '未知'}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>行为类型:</strong> ${video.behavior_type}<br>
                        <strong>置信度:</strong> ${(video.cat_confidence * 100).toFixed(1)}%
                    </div>
                </div>
            `).join('');

            updateDownloadButton();
        }

        // 渲染本地视频网格
        function renderLocalVideoGrid(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card local-video-card">
                    <div class="video-info">
                        <div class="video-header">
                            <strong>视频ID:</strong> <span class="video-id">${video.video_id}</span>
                        </div>
                        <div class="video-details">
                            <div class="detail-row">
                                <strong>设备ID:</strong> <span>${video.device_id}</span>
                            </div>
                            <div class="detail-row">
                                <strong>猫ID:</strong> <span>${video.animal_id || '未知'}</span>
                            </div>
                            <div class="detail-row">
                                <strong>开始时间:</strong> <span>${formatTimestamp(video.start_time)}</span>
                            </div>
                            <div class="detail-row">
                                <strong>文件大小:</strong> <span>${formatBytes(video.file_size)}</span>
                            </div>
                            <div class="detail-row">
                                <strong>文件数量:</strong> <span>${video.file_count}</span>
                            </div>
                            <div class="detail-row">
                                <strong>下载时间:</strong> <span>${new Date(video.downloaded_at).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="video-actions">
                        <button class="btn btn-danger" onclick="deleteVideo('${video.video_id}')">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(currentPage, totalPages, containerId, loadFunction) {
            const container = document.getElementById(containerId);
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let pagination = '';

            // 上一页
            if (currentPage > 1) {
                pagination += `<button class="btn btn-primary" onclick="${loadFunction.name}(${currentPage - 1})">上一页</button>`;
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const active = i === currentPage ? 'btn-success' : 'btn-primary';
                pagination += `<button class="btn ${active}" onclick="${loadFunction.name}(${i})">${i}</button>`;
            }

            // 下一页
            if (currentPage < totalPages) {
                pagination += `<button class="btn btn-primary" onclick="${loadFunction.name}(${currentPage + 1})">下一页</button>`;
            }

            container.innerHTML = pagination;
        }

        // 切换视频选择状态
        function toggleVideoSelection(videoId) {
            if (selectedVideos.has(videoId)) {
                selectedVideos.delete(videoId);
            } else {
                selectedVideos.add(videoId);
            }

            // 更新UI
            const card = event.target.closest('.video-card');
            card.classList.toggle('selected');
            updateDownloadButton();
        }

        // 全选
        function selectAll() {
            document.querySelectorAll('#remote-videos .video-card').forEach(card => {
                const videoId = card.textContent.match(/视频ID:\s*([^\s]+)/)[1];
                selectedVideos.add(videoId);
                card.classList.add('selected');
            });
            updateDownloadButton();
        }

        // 清空选择
        function clearSelection() {
            selectedVideos.clear();
            document.querySelectorAll('.video-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateDownloadButton();
        }

        // 更新下载按钮状态
        function updateDownloadButton() {
            const btn = document.getElementById('download-btn');
            btn.disabled = selectedVideos.size === 0;
            btn.textContent = `下载选中 (${selectedVideos.size})`;
        }

        // 下载选中的视频
        async function downloadSelected() {
            if (selectedVideos.size === 0) return;

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                alert(`下载任务已创建，任务数量: ${data.task_ids.length}`);
                clearSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            }
        }

        // 加载任务列表
        async function loadTasks() {
            const container = document.getElementById('tasks');

            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderTasks(data.tasks, container);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染任务列表
        function renderTasks(tasks, container) {
            if (tasks.length === 0) {
                container.innerHTML = '<div class="loading">暂无任务</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-info">
                        <strong>${task.video_info.video_id}</strong> - ${task.video_info.device_id}
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${task.progress}%"></div>
                        </div>
                        <small>${task.progress.toFixed(1)}% - ${task.error || ''}</small>
                    </div>
                    <div>
                        <span class="task-status status-${task.status}">${getStatusText(task.status)}</span>
                        ${task.status === 'downloading' || task.status === 'pending' ?
                            `<button class="btn btn-danger" onclick="cancelTask('${task.id}')">取消</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'downloading': '下载中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 取消任务
        async function cancelTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '取消失败');
                }

                loadTasks();
            } catch (error) {
                alert(`取消任务失败: ${error.message}`);
            }
        }

        // 删除本地视频
        async function deleteVideo(videoId) {
            if (!confirm('确定要删除这个视频吗？')) return;

            try {
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '删除失败');
                }

                loadLocalVideos();
                loadStats();
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        // 清理存储
        async function cleanupStorage() {
            try {
                const response = await fetch('/api/storage/cleanup', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '清理失败');
                }

                alert('存储清理完成');
                loadStats();
            } catch (error) {
                alert(`清理失败: ${error.message}`);
            }
        }

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTimestamp(timestamp) {
            return new Date(timestamp * 1000).toLocaleString();
        }

        // ==================== 同步管理功能 ====================

        // 加载同步摘要
        async function loadSyncSummary() {
            const summaryContainer = document.getElementById('sync-summary');
            summaryContainer.innerHTML = '<div class="loading">加载同步状态...</div>';

            try {
                const response = await fetch('/api/sync/summary');
                const summary = await response.json();

                if (!response.ok) {
                    throw new Error(summary.error || '加载失败');
                }

                renderSyncSummary(summary, summaryContainer);
                loadFilteredVideos(); // 加载筛选后的视频列表

            } catch (error) {
                summaryContainer.innerHTML = `<div class="error">加载同步状态失败: ${error.message}</div>`;
            }
        }

        // 渲染同步摘要
        function renderSyncSummary(summary, container) {
            container.innerHTML = `
                <div class="sync-card synced">
                    <h4>已同步</h4>
                    <div class="count">${summary.synced}</div>
                </div>
                <div class="sync-card remote-only">
                    <h4>仅云端存在</h4>
                    <div class="count">${summary.remote_only}</div>
                </div>
                <div class="sync-card local-only">
                    <h4>仅本地存在</h4>
                    <div class="count">${summary.local_only}</div>
                </div>
                <div class="sync-card conflicts">
                    <h4>冲突</h4>
                    <div class="count">${summary.conflicts}</div>
                </div>
                <div class="sync-card">
                    <h4>云端总数</h4>
                    <div class="count">${summary.total_remote}</div>
                </div>
                <div class="sync-card">
                    <h4>本地总数</h4>
                    <div class="count">${summary.total_local}</div>
                </div>
            `;
        }

        // 加载筛选后的视频列表
        async function loadFilteredVideos(page = 1) {
            const container = document.getElementById('sync-videos');
            const filter = document.getElementById('sync-filter').value;

            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/sync/videos?filter=${filter}&page=${page}&page_size=20`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderSyncVideos(data.videos, container);
                renderPagination(data.page, data.total_pages, 'sync-pagination', loadFilteredVideos);

            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染同步视频列表
        function renderSyncVideos(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>状态:</strong> <span class="sync-status ${video.sync_status}">${getSyncStatusText(video.sync_status)}</span><br>
                        <strong>本地存在:</strong> ${video.local_exists ? '是' : '否'}<br>
                        <strong>云端存在:</strong> ${video.remote_exists ? '是' : '否'}<br>
                        ${video.local_file_size ? `<strong>本地大小:</strong> ${formatBytes(video.local_file_size)}<br>` : ''}
                        ${video.last_sync_time ? `<strong>同步时间:</strong> ${new Date(video.last_sync_time).toLocaleString()}<br>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 获取同步状态文本
        function getSyncStatusText(status) {
            const statusMap = {
                'synced': '已同步',
                'remote_only': '仅云端',
                'local_only': '仅本地',
                'conflict': '冲突'
            };
            return statusMap[status] || status;
        }

        // 刷新同步数据库
        async function refreshSyncDatabase() {
            try {
                const response = await fetch('/api/sync/refresh', {
                    method: 'POST'
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '刷新失败');
                }

                alert('同步数据库已刷新');
                loadSyncSummary();

            } catch (error) {
                alert(`刷新失败: ${error.message}`);
            }
        }

        // ==================== 分类管理功能 ====================

        let currentDeviceID = null;
        let currentCatID = null;

        // 显示分类视图
        function showCategoryView() {
            document.getElementById('normal-view').style.display = 'none';
            document.getElementById('category-view').style.display = 'block';
        }

        // 隐藏分类视图
        function hideCategoryView() {
            document.getElementById('category-view').style.display = 'none';
            document.getElementById('device-view').style.display = 'none';
            document.getElementById('cat-view').style.display = 'none';
            document.getElementById('normal-view').style.display = 'block';

            // 清空分类选择状态
            currentDeviceID = null;
            currentCatID = null;

            // 重新加载普通列表
            loadLocalVideos();
        }

        // 显示设备视图
        async function showDeviceView() {
            document.getElementById('device-view').style.display = 'block';
            document.getElementById('cat-view').style.display = 'none';

            // 清空设备视频显示
            document.getElementById('device-videos').innerHTML = '';
            document.getElementById('device-pagination').innerHTML = '';
            currentDeviceID = null;

            await loadDeviceList();
        }

        // 显示猫视图
        async function showCatView() {
            document.getElementById('cat-view').style.display = 'block';
            document.getElementById('device-view').style.display = 'none';

            // 清空猫视频显示
            document.getElementById('cat-videos').innerHTML = '';
            document.getElementById('cat-pagination').innerHTML = '';
            currentCatID = null;

            await loadCatList();
        }

        // 加载设备列表
        async function loadDeviceList() {
            const container = document.getElementById('device-list');
            container.innerHTML = '<div class="loading">加载设备列表...</div>';

            try {
                const [devicesResponse, statsResponse] = await Promise.all([
                    fetch('/api/categories/devices'),
                    fetch('/api/categories/devices/stats')
                ]);

                const devicesData = await devicesResponse.json();
                const statsData = await statsResponse.json();

                if (!devicesResponse.ok || !statsResponse.ok) {
                    throw new Error('加载设备数据失败');
                }

                renderDeviceList(devicesData.devices, statsData, container);

            } catch (error) {
                container.innerHTML = `<div class="error">加载设备列表失败: ${error.message}</div>`;
            }
        }

        // 渲染设备列表
        function renderDeviceList(devices, stats, container) {
            if (devices.length === 0) {
                container.innerHTML = '<div class="loading">暂无设备</div>';
                return;
            }

            container.innerHTML = devices.map(deviceID => {
                const deviceStats = stats[deviceID] || { video_count: 0, total_size: 0 };
                return `
                    <div class="category-item" onclick="selectDevice('${deviceID}')">
                        <div class="name">${deviceID}</div>
                        <div class="stats">${deviceStats.video_count} 个视频 | ${formatBytes(deviceStats.total_size)}</div>
                    </div>
                `;
            }).join('');
        }

        // 选择设备
        async function selectDevice(deviceID) {
            currentDeviceID = deviceID;

            // 更新选中状态
            document.querySelectorAll('#device-list .category-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.category-item').classList.add('active');

            // 加载设备视频
            await loadDeviceVideos(deviceID);
        }

        // 加载设备视频
        async function loadDeviceVideos(deviceID, page = 1) {
            const container = document.getElementById('device-videos');
            container.innerHTML = '<div class="loading">加载视频...</div>';

            try {
                const response = await fetch(`/api/categories/devices/${deviceID}/videos?page=${page}&page_size=20`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'device-pagination', (p) => loadDeviceVideos(deviceID, p));

            } catch (error) {
                container.innerHTML = `<div class="error">加载设备视频失败: ${error.message}</div>`;
            }
        }

        // 加载猫列表
        async function loadCatList() {
            const container = document.getElementById('cat-list');
            container.innerHTML = '<div class="loading">加载猫列表...</div>';

            try {
                const [catsResponse, statsResponse] = await Promise.all([
                    fetch('/api/categories/cats'),
                    fetch('/api/categories/cats/stats')
                ]);

                const catsData = await catsResponse.json();
                const statsData = await statsResponse.json();

                if (!catsResponse.ok || !statsResponse.ok) {
                    throw new Error('加载猫数据失败');
                }

                renderCatList(catsData.cats, statsData, container);

            } catch (error) {
                container.innerHTML = `<div class="error">加载猫列表失败: ${error.message}</div>`;
            }
        }

        // 渲染猫列表
        function renderCatList(cats, stats, container) {
            if (cats.length === 0) {
                container.innerHTML = '<div class="loading">暂无猫咪</div>';
                return;
            }

            container.innerHTML = cats.map(catID => {
                const catStats = stats[catID] || { video_count: 0, total_size: 0 };
                return `
                    <div class="category-item" onclick="selectCat('${catID}')">
                        <div class="name">${catID}</div>
                        <div class="stats">${catStats.video_count} 个视频 | ${formatBytes(catStats.total_size)}</div>
                    </div>
                `;
            }).join('');
        }

        // 选择猫
        async function selectCat(catID) {
            currentCatID = catID;

            // 更新选中状态
            document.querySelectorAll('#cat-list .category-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.category-item').classList.add('active');

            // 加载猫视频
            await loadCatVideos(catID);
        }

        // 加载猫视频
        async function loadCatVideos(catID, page = 1) {
            const container = document.getElementById('cat-videos');
            container.innerHTML = '<div class="loading">加载视频...</div>';

            try {
                const response = await fetch(`/api/categories/cats/${catID}/videos?page=${page}&page_size=20`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'cat-pagination', (p) => loadCatVideos(catID, p));

            } catch (error) {
                container.innerHTML = `<div class="error">加载猫视频失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
